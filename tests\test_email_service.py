#!/usr/bin/env python3
"""
Test script for Email Service functionality

This script tests the email service functionality including:
- Email configuration validation
- Template rendering
- Mock email sending
- Integration with backup results

Usage:
    python test_email_service.py
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add parent directory to path to import core modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import the email service
try:
    from core.email_service import EmailService, EmailServiceError
except ImportError as e:
    print(f"❌ Error importing email service: {e}")
    print("Please ensure core modules are available")
    sys.exit(1)


def setup_logging():
    """Setup logging for testing."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def create_test_backup_results() -> Dict[str, Any]:
    """Create test backup results for email testing."""
    return {
        'status': 'completed',
        'total_dates': 2,
        'total_tables': 5,
        'overall_duration': 1800,  # 30 minutes
        'total_rows_backed_up': 125000,
        'date_results': [
            {
                'date': '2025-06-25',
                'completed': 4,
                'failed': 1,
                'no_data': 0,
                'total_rows': 75000,
                'table_results': [
                    {
                        'table_name': 'my.app.tngd.waf',
                        'status': 'completed',
                        'rows': 25000,
                        'duration': 120.5,
                        'error': None
                    },
                    {
                        'table_name': 'my.app.tngd.actiontraillinux',
                        'status': 'completed',
                        'rows': 18000,
                        'duration': 95.2,
                        'error': None
                    },
                    {
                        'table_name': 'cloud.office365.management.exchange',
                        'status': 'completed',
                        'rows': 32000,
                        'duration': 180.8,
                        'error': None
                    },
                    {
                        'table_name': 'cloud.alibaba.log_service.events',
                        'status': 'failed',
                        'rows': 0,
                        'duration': 45.0,
                        'error': 'Connection timeout after 30 minutes'
                    },
                    {
                        'table_name': 'cef0.zscaler.nssweblog',
                        'status': 'completed',
                        'rows': 0,
                        'duration': 15.3,
                        'error': None
                    }
                ]
            },
            {
                'date': '2025-06-26',
                'completed': 5,
                'failed': 0,
                'no_data': 0,
                'total_rows': 50000,
                'table_results': [
                    {
                        'table_name': 'my.app.tngd.waf',
                        'status': 'completed',
                        'rows': 15000,
                        'duration': 85.2,
                        'error': None
                    },
                    {
                        'table_name': 'my.app.tngd.actiontraillinux',
                        'status': 'completed',
                        'rows': 12000,
                        'duration': 72.1,
                        'error': None
                    },
                    {
                        'table_name': 'cloud.office365.management.exchange',
                        'status': 'completed',
                        'rows': 18000,
                        'duration': 110.5,
                        'error': None
                    },
                    {
                        'table_name': 'cloud.alibaba.log_service.events',
                        'status': 'completed',
                        'rows': 3000,
                        'duration': 25.8,
                        'error': None
                    },
                    {
                        'table_name': 'cef0.zscaler.nssweblog',
                        'status': 'completed',
                        'rows': 2000,
                        'duration': 18.9,
                        'error': None
                    }
                ]
            }
        ]
    }


def create_failed_backup_results() -> Dict[str, Any]:
    """Create test backup results for failed backup scenario."""
    return {
        'status': 'connection_failed',
        'total_dates': 1,
        'total_tables': 5,
        'overall_duration': 30,
        'total_rows_backed_up': 0,
        'date_results': [],
        'error': 'Connection tests failed'
    }


def test_email_service_initialization():
    """Test email service initialization."""
    print("\n🧪 Testing Email Service Initialization...")
    
    try:
        email_service = EmailService()
        print("✅ Email service initialized successfully")
        
        # Check configuration
        print(f"   SMTP Server: {email_service.smtp_server}:{email_service.smtp_port}")
        print(f"   Sender: {email_service.sender_email}")
        print(f"   Receiver: {email_service.receiver_email}")
        print(f"   Mock Mode: {email_service.mock_mode}")
        
        return email_service
        
    except EmailServiceError as e:
        print(f"❌ Email service initialization failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error during initialization: {e}")
        return None


def test_email_connection(email_service: EmailService):
    """Test email connection."""
    print("\n🧪 Testing Email Connection...")
    
    try:
        if email_service.test_email_connection():
            print("✅ Email connection test passed")
            return True
        else:
            print("❌ Email connection test failed")
            return False
    except Exception as e:
        print(f"❌ Error during connection test: {e}")
        return False


def test_template_rendering(email_service: EmailService):
    """Test email template rendering."""
    print("\n🧪 Testing Email Template Rendering...")
    
    try:
        # Test with successful backup results
        test_results = create_test_backup_results()
        html_content = email_service._create_backup_summary_html(test_results)
        
        if html_content and len(html_content) > 1000:
            print("✅ HTML template rendering successful")
            print(f"   Generated HTML length: {len(html_content)} characters")
            
            # Save sample HTML for inspection
            sample_path = os.path.join('..', 'docs', 'sample_email_report.html')
            os.makedirs(os.path.join('..', 'docs'), exist_ok=True)
            with open(sample_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"   Sample HTML saved to: {sample_path}")
            
            return True
        else:
            print("❌ HTML template rendering failed - content too short")
            return False
            
    except Exception as e:
        print(f"❌ Error during template rendering: {e}")
        return False


def test_email_sending(email_service: EmailService):
    """Test email sending functionality."""
    print("\n🧪 Testing Email Sending...")
    
    try:
        # Test successful backup email
        print("   Testing successful backup email...")
        test_results = create_test_backup_results()
        
        if email_service.send_backup_summary(test_results):
            print("✅ Successful backup email sent")
        else:
            print("❌ Failed to send successful backup email")
            return False
        
        # Test failed backup email
        print("   Testing failed backup email...")
        failed_results = create_failed_backup_results()
        
        if email_service.send_backup_summary(failed_results):
            print("✅ Failed backup email sent")
        else:
            print("❌ Failed to send failed backup email")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during email sending test: {e}")
        return False


def test_email_service_integration():
    """Test email service integration with backup system."""
    print("\n🧪 Testing Email Service Integration...")
    
    try:
        # This would normally be tested with the actual backup system
        # For now, we'll just test the email service independently
        email_service = EmailService()
        
        if email_service.send_test_email():
            print("✅ Email service integration test passed")
            return True
        else:
            print("❌ Email service integration test failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during integration test: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Starting Email Service Tests")
    print("=" * 50)
    
    setup_logging()
    
    # Test results tracking
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Email Service Initialization
    email_service = test_email_service_initialization()
    if email_service:
        tests_passed += 1
    else:
        print("❌ Cannot continue tests without email service")
        sys.exit(1)
    
    # Test 2: Email Connection
    if test_email_connection(email_service):
        tests_passed += 1
    
    # Test 3: Template Rendering
    if test_template_rendering(email_service):
        tests_passed += 1
    
    # Test 4: Email Sending
    if test_email_sending(email_service):
        tests_passed += 1
    
    # Test 5: Integration Test
    if test_email_service_integration():
        tests_passed += 1
    
    # Final Results
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Email service is ready for use.")
        sys.exit(0)
    else:
        print(f"⚠️  {total_tests - tests_passed} test(s) failed. Please review the issues above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
