#!/usr/bin/env python3
"""
Test script to verify table loading functionality
"""

import sys
import json
from pathlib import Path

# Add src to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_table_files():
    """Test if table configuration files exist and are valid."""
    print("🔍 Testing table configuration files...")
    
    # Test main tables.json
    tables_file = Path("config/tables.json")
    if tables_file.exists():
        with open(tables_file, 'r') as f:
            tables = json.load(f)
        print(f"✅ config/tables.json: {len(tables)} tables")
    else:
        print("❌ config/tables.json: NOT FOUND")
    
    # Test production tables
    prod_tables_file = Path("config/tables_production.json")
    if prod_tables_file.exists():
        with open(prod_tables_file, 'r') as f:
            prod_tables = json.load(f)
        print(f"✅ config/tables_production.json: {len(prod_tables)} tables")
    else:
        print("❌ config/tables_production.json: NOT FOUND")
    
    # Test developer tables
    dev_tables_file = Path("config/tables_developer.json")
    if dev_tables_file.exists():
        with open(dev_tables_file, 'r') as f:
            dev_tables = json.load(f)
        print(f"✅ config/tables_developer.json: {len(dev_tables)} tables")
    else:
        print("❌ config/tables_developer.json: NOT FOUND")

def test_config_files():
    """Test if configuration files exist."""
    print("\n🔍 Testing configuration files...")
    
    configs = [
        "config/default.json",
        "config/production.json", 
        "config/development.json"
    ]
    
    for config_file in configs:
        if Path(config_file).exists():
            print(f"✅ {config_file}: EXISTS")
        else:
            print(f"❌ {config_file}: NOT FOUND")

def test_backup_engine_import():
    """Test if backup engine can be imported."""
    print("\n🔍 Testing backup engine import...")
    
    try:
        from tngd_backup.core.backup_engine import BackupEngine
        print("✅ BackupEngine import: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ BackupEngine import: FAILED - {e}")
        return False

def test_table_loading_logic():
    """Test the table loading logic directly."""
    print("\n🔍 Testing table loading logic...")
    
    try:
        from tngd_backup.core.backup_engine import BackupEngine
        from datetime import datetime
        
        # Test with development config
        print("\n📋 Testing DEVELOPMENT mode:")
        dev_engine = BackupEngine([datetime.now()], "config/development.json")
        dev_tables = dev_engine.load_tables()
        print(f"   Developer tables loaded: {len(dev_tables)}")
        print(f"   First 3 tables: {dev_tables[:3]}")
        
        # Test with production config
        print("\n📋 Testing PRODUCTION mode:")
        prod_engine = BackupEngine([datetime.now()], "config/production.json")
        prod_tables = prod_engine.load_tables()
        print(f"   Production tables loaded: {len(prod_tables)}")
        print(f"   First 3 tables: {prod_tables[:3]}")
        
        # Test with default config
        print("\n📋 Testing DEFAULT mode:")
        default_engine = BackupEngine([datetime.now()], "config/default.json")
        default_tables = default_engine.load_tables()
        print(f"   Default tables loaded: {len(default_tables)}")
        print(f"   First 3 tables: {default_tables[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Table loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 TNGD Backup System - Table Loading Test")
    print("=" * 50)
    
    test_table_files()
    test_config_files()
    
    if test_backup_engine_import():
        test_table_loading_logic()
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")

if __name__ == "__main__":
    main()
