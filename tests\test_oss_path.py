#!/usr/bin/env python3
"""
Test script to verify OSS path generation matches your actual structure
"""

from datetime import datetime
import calendar

def test_oss_path_generation():
    """Test the updated OSS path generation."""
    
    print("=== Testing Updated OSS Path Generation ===")
    
    # Test parameters
    target_date = datetime(2025, 4, 14)
    table_name = 'my.app.tngd.actiontraillinux'
    
    # Generate filename with date (new format)
    date_str = target_date.strftime('%Y-%m-%d')
    filename = f"{table_name.replace('.', '_')}_{date_str}.tar.gz"
    
    # Generate path parts (without backup_run_id)
    path_parts = []
    path_parts.append('Devo')  # provider_path
    path_parts.append(calendar.month_name[target_date.month])  # April
    week_of_month = ((target_date.day - 1) // 7) + 1
    path_parts.append(f'week {week_of_month}')  # week 2
    path_parts.append(date_str)  # 2025-04-14
    # No backup_run_id folder (include_backup_run_id: false)
    path_parts.append(filename)
    
    upload_path = '/'.join(path_parts)
    
    print(f"Updated system path: {upload_path}")
    print(f"Your actual path:    Devo/April/week 2/2025-04-14/my_app_tngd_actiontraillinux_2025-04-14.tar.gz")
    
    # Check if they match
    expected_path = "Devo/April/week 2/2025-04-14/my_app_tngd_actiontraillinux_2025-04-14.tar.gz"
    
    if upload_path == expected_path:
        print("✅ PERFECT MATCH! OSS path generation is now correct.")
    else:
        print("❌ Paths don't match. Let me check the differences:")
        print(f"   Expected: {expected_path}")
        print(f"   Generated: {upload_path}")
        
        # Find differences
        expected_parts = expected_path.split('/')
        generated_parts = upload_path.split('/')
        
        for i, (exp, gen) in enumerate(zip(expected_parts, generated_parts)):
            if exp != gen:
                print(f"   Difference at part {i}: expected '{exp}', got '{gen}'")
    
    return upload_path == expected_path

if __name__ == "__main__":
    success = test_oss_path_generation()
    if success:
        print("\n🎉 OSS path configuration is now correct!")
    else:
        print("\n⚠️ OSS path needs further adjustment.")
