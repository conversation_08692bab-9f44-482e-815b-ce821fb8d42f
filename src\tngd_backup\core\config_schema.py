#!/usr/bin/env python3
"""
Configuration Schema Validation for TNGD Backup System

This module provides schema validation for configuration files to ensure
proper structure and data types.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigValidationError(Exception):
    """Exception raised when configuration validation fails."""
    pass


class ConfigSchema:
    """Configuration schema validator for TNGD backup system."""
    
    def __init__(self):
        """Initialize the configuration schema validator."""
        self.required_fields = {
            'devo_credentials': {
                'type': dict,
                'required_keys': ['endpoint', 'key', 'secret']
            },
            'oss_credentials': {
                'type': dict,
                'required_keys': ['endpoint', 'access_key_id', 'access_key_secret', 'bucket_name']
            },
            'email_settings': {
                'type': dict,
                'required_keys': ['smtp_server', 'smtp_port', 'sender_email', 'sender_password', 'receiver_emails']
            }
        }
        
        self.optional_fields = {
            'resource_limits': dict,
            'query_settings': dict,
            'storage_settings': dict,
            'monitoring_settings': dict
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate configuration against schema.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            True if valid, raises ConfigValidationError if invalid
        """
        try:
            # Check required fields
            for field_name, field_spec in self.required_fields.items():
                if field_name not in config:
                    raise ConfigValidationError(f"Missing required field: {field_name}")
                
                field_value = config[field_name]
                
                # Check field type
                if not isinstance(field_value, field_spec['type']):
                    raise ConfigValidationError(
                        f"Field '{field_name}' must be of type {field_spec['type'].__name__}, "
                        f"got {type(field_value).__name__}"
                    )
                
                # Check required keys for dict fields
                if field_spec['type'] == dict and 'required_keys' in field_spec:
                    for required_key in field_spec['required_keys']:
                        if required_key not in field_value:
                            raise ConfigValidationError(
                                f"Missing required key '{required_key}' in field '{field_name}'"
                            )
            
            # Validate credential formats
            self._validate_credentials(config)
            
            logger.info("Configuration schema validation passed")
            return True
            
        except ConfigValidationError:
            raise
        except Exception as e:
            raise ConfigValidationError(f"Unexpected validation error: {str(e)}")
    
    def _validate_credentials(self, config: Dict[str, Any]) -> None:
        """Validate credential formats and basic structure."""
        # Validate Devo credentials
        devo_creds = config.get('devo_credentials', {})
        if devo_creds.get('endpoint') and not devo_creds['endpoint'].startswith('https://'):
            logger.warning("Devo endpoint should use HTTPS")
        
        # Validate OSS credentials
        oss_creds = config.get('oss_credentials', {})
        if oss_creds.get('endpoint') and not oss_creds['endpoint'].startswith('https://'):
            logger.warning("OSS endpoint should use HTTPS")
        
        # Validate email settings
        email_settings = config.get('email_settings', {})
        if email_settings.get('receiver_emails'):
            if isinstance(email_settings['receiver_emails'], str):
                # Convert comma-separated string to list
                emails = [email.strip() for email in email_settings['receiver_emails'].split(',')]
                for email in emails:
                    if '@' not in email:
                        raise ConfigValidationError(f"Invalid email format: {email}")
            elif isinstance(email_settings['receiver_emails'], list):
                for email in email_settings['receiver_emails']:
                    if '@' not in email:
                        raise ConfigValidationError(f"Invalid email format: {email}")
    
    def get_schema_info(self) -> Dict[str, Any]:
        """Get information about the configuration schema."""
        return {
            'required_fields': list(self.required_fields.keys()),
            'optional_fields': list(self.optional_fields.keys()),
            'version': '2.0'
        }
