#!/usr/bin/env python3
"""
Streaming Implementation Test Suite

This script tests the new streaming/chunking functionality for handling large datasets
in the TNGD backup system. It validates backward compatibility, error handling,
and proper integration with existing systems.

Usage:
    python test_streaming_implementation.py
"""

import os
import sys
import json
import time
import tempfile
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add parent directory to path to import core modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from core.streaming_processor import StreamingDataProcessor, StreamingConfig, ChunkManager, ProgressTracker
    from core.config_manager import ConfigManager
    from core.resource_monitor import ResourceMonitor
    from tngd_backup import TngdBackup
except ImportError as e:
    print(f"❌ Error importing modules: {e}")
    print("Please ensure all core modules are available")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StreamingTestSuite:
    """Test suite for streaming implementation."""
    
    def __init__(self):
        """Initialize test suite."""
        self.test_results = []
        self.temp_files = []
        
    def log_test_result(self, test_name: str, success: bool, message: str = "", duration: float = 0):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        duration_str = f" ({duration:.2f}s)" if duration > 0 else ""
        print(f"{status} {test_name}{duration_str}")
        if message:
            print(f"    {message}")
        
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'message': message,
            'duration': duration
        })
    
    def cleanup_temp_files(self):
        """Clean up temporary files created during tests."""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"Failed to clean up {temp_file}: {e}")
        self.temp_files.clear()
    
    def test_streaming_config_initialization(self):
        """Test streaming configuration initialization."""
        test_name = "Streaming Configuration Initialization"
        start_time = time.time()
        
        try:
            # Test with default configuration
            config = StreamingConfig()
            assert config.default_chunk_size == 100000
            assert config.streaming_threshold_rows == 1000000
            assert config.enable_adaptive_chunking == True
            
            # Test with custom configuration
            custom_config = StreamingConfig(
                default_chunk_size=50000,
                streaming_threshold_rows=500000,
                enable_adaptive_chunking=False
            )
            assert custom_config.default_chunk_size == 50000
            assert custom_config.streaming_threshold_rows == 500000
            assert custom_config.enable_adaptive_chunking == False
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, "Configuration initialized correctly", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"Error: {str(e)}", duration)
    
    def test_chunk_manager(self):
        """Test chunk manager functionality."""
        test_name = "Chunk Manager"
        start_time = time.time()
        
        try:
            config = StreamingConfig(default_chunk_size=1000, min_chunk_size=500)
            chunk_manager = ChunkManager(config)

            # Test chunk calculation for small dataset
            chunks = chunk_manager.calculate_chunks(500, "test_table")
            assert len(chunks) == 1
            assert chunks[0].limit == 500
            assert chunks[0].offset == 0

            # Test chunk calculation for medium dataset
            chunks = chunk_manager.calculate_chunks(1500, "test_table")
            assert len(chunks) == 3  # 1500 rows with 500 chunk size = 3 chunks
            assert chunks[0].limit == 500
            assert chunks[1].limit == 500
            assert chunks[2].limit == 500

            # Test chunk size adjustment
            new_size = chunk_manager.adjust_chunk_size(1000, memory_pressure=True)
            assert new_size == 800  # 80% of original
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, f"Calculated chunks correctly for various sizes", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            import traceback
            error_details = traceback.format_exc()
            self.log_test_result(test_name, False, f"Error: {str(e)}\n{error_details}", duration)
    
    def test_progress_tracker(self):
        """Test progress tracking functionality."""
        test_name = "Progress Tracker"
        start_time = time.time()
        
        try:
            # Mock log function
            log_messages = []
            def mock_log(step, message, level="INFO"):
                log_messages.append(f"{step}: {message}")
            
            # Create progress tracker
            tracker = ProgressTracker(5, 5000, mock_log)
            
            # Simulate processing chunks
            from core.streaming_processor import ChunkInfo
            for i in range(3):
                chunk = ChunkInfo(
                    chunk_id=i+1,
                    offset=i*1000,
                    limit=1000,
                    actual_rows=1000,
                    processing_time=0.5
                )
                tracker.update_progress(chunk)
            
            # Check summary
            summary = tracker.get_summary()
            assert summary['processed_chunks'] == 3
            assert summary['processed_rows'] == 3000
            assert len(log_messages) >= 3
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, f"Progress tracked correctly", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"Error: {str(e)}", duration)
    
    def test_resource_monitor(self):
        """Test resource monitoring functionality."""
        test_name = "Resource Monitor"
        start_time = time.time()
        
        try:
            monitor = ResourceMonitor()
            
            # Get current metrics
            metrics = monitor.get_current_metrics()
            assert hasattr(metrics, 'cpu_percent')
            assert hasattr(metrics, 'memory_percent')
            assert hasattr(metrics, 'disk_percent')
            assert metrics.alert_level in ['normal', 'warning', 'critical', 'unknown', 'error']
            
            # Test throttling (should not raise errors)
            monitor.apply_throttling("test_operation")
            monitor.log_resource_status("test_operation")
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, f"Resource monitoring working", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"Error: {str(e)}", duration)
    
    def test_streaming_decision_logic(self):
        """Test streaming decision logic."""
        test_name = "Streaming Decision Logic"
        start_time = time.time()
        
        try:
            # Mock log function
            def mock_log(step, message, level="INFO"):
                pass
            
            config = StreamingConfig(streaming_threshold_rows=1000, memory_threshold_mb=99999)  # Very high memory threshold
            processor = StreamingDataProcessor(config, mock_log)
            # Disable resource monitor for predictable testing
            processor.resource_monitor = None
            
            # Test small dataset (should not use streaming)
            should_stream = processor.should_use_streaming(500, "small_table")
            assert should_stream == False

            # Test large dataset (should use streaming)
            should_stream = processor.should_use_streaming(2000, "large_table")
            assert should_stream == True
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, "Decision logic working correctly", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            import traceback
            error_details = traceback.format_exc()
            self.log_test_result(test_name, False, f"Error: {str(e)}\n{error_details}", duration)
    
    def test_config_integration(self):
        """Test configuration integration."""
        test_name = "Configuration Integration"
        start_time = time.time()
        
        try:
            # Test loading configuration
            config_manager = ConfigManager()
            
            # Check if streaming configuration exists
            chunking_config = config_manager.get('processing', 'chunking', {})
            streaming_config = config_manager.get('processing', 'streaming', {})
            
            assert isinstance(chunking_config, dict)
            assert isinstance(streaming_config, dict)
            
            # Test TngdBackup initialization with streaming
            backup = TngdBackup()
            assert backup.streaming_config is not None
            assert hasattr(backup.streaming_config, 'default_chunk_size')
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, "Configuration integration working", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"Error: {str(e)}", duration)
    
    def test_backward_compatibility(self):
        """Test backward compatibility with existing functionality."""
        test_name = "Backward Compatibility"
        start_time = time.time()
        
        try:
            # Test that existing methods still work
            backup = TngdBackup()
            
            # Test log_step method
            backup.log_step("TEST", "Testing backward compatibility")
            
            # Test configuration loading
            tables = backup.load_tables()
            assert isinstance(tables, list)
            
            # Test timeout calculation
            timeout = backup.get_timeout_for_table("test.table")
            assert isinstance(timeout, int)
            assert timeout > 0
            
            duration = time.time() - start_time
            self.log_test_result(test_name, True, "Backward compatibility maintained", duration)
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test_result(test_name, False, f"Error: {str(e)}", duration)
    
    def run_all_tests(self):
        """Run all tests in the suite."""
        print("🚀 Starting Streaming Implementation Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run individual tests
        self.test_streaming_config_initialization()
        self.test_chunk_manager()
        self.test_progress_tracker()
        self.test_resource_monitor()
        self.test_streaming_decision_logic()
        self.test_config_integration()
        self.test_backward_compatibility()
        
        # Clean up
        self.cleanup_temp_files()
        
        # Summary
        total_time = time.time() - start_time
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print("=" * 60)
        print(f"📊 Test Summary: {passed}/{total} tests passed in {total_time:.2f}s")
        
        if passed == total:
            print("🎉 All tests passed! Streaming implementation is ready.")
            return True
        else:
            print("⚠️  Some tests failed. Please review the implementation.")
            return False


def main():
    """Main test function."""
    test_suite = StreamingTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Streaming implementation validation completed successfully!")
        print("\n📋 Summary of new capabilities:")
        print("   • Automatic streaming for datasets > 1M rows")
        print("   • Configurable chunk sizes and thresholds")
        print("   • Memory-efficient processing with progress tracking")
        print("   • Resource monitoring and adaptive throttling")
        print("   • Backward compatibility with existing functionality")
        print("   • Enhanced error handling and retry logic")
        sys.exit(0)
    else:
        print("\n❌ Streaming implementation validation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
