{"version": "2.0", "description": "TNGD Backup System - Large Dataset Optimized Configuration", "resource_management": {"max_threads": 1, "memory_threshold_mb": 3000, "cpu_threshold_percent": 90, "disk_threshold_percent": 85, "cleanup_interval_seconds": 60, "resource_check_interval_seconds": 30, "thread_pool_timeout_seconds": 600, "force_cleanup_threshold": 500, "enable_memory_profiling": true}, "query_settings": {"default_timeout_seconds": 3600, "large_table_timeout_seconds": 7200, "max_retries": 5, "retry_delay_seconds": 120, "connection_timeout_seconds": 60, "read_timeout_seconds": 600, "query_heartbeat_interval": 60, "enable_query_cancellation": true, "progressive_timeout": true}, "streaming_config": {"enabled": true, "default_chunk_size": 5000, "max_chunk_size": 10000, "min_chunk_size": 1000, "streaming_threshold_rows": 10000, "memory_threshold_mb": 500, "progress_report_interval": 20, "memory_check_interval": 2, "enable_adaptive_chunking": true, "chunk_size_adjustment_factor": 0.3, "temp_file_cleanup": true, "aggressive_memory_management": true, "gc_interval": 1, "force_streaming_for_large_tables": true}, "storage_settings": {"upload_timeout_seconds": 1200, "max_upload_retries": 5, "retry_delay_seconds": 60, "chunk_size_mb": 10, "memory_threshold_percent": 60, "connection_pool_size": 1, "verify_integrity": true, "compress_before_upload": true, "parallel_uploads": false, "upload_queue_size": 3, "sequential_processing": true, "upload_path_structure": {"base_path": "", "provider_path": "Devo", "use_month_folders": true, "use_week_folders": true, "use_date_folders": true, "include_table_folders": false}}, "monitoring": {"enabled": true, "log_level": "INFO", "metrics_collection": true, "health_check_interval_seconds": 60, "thread_metrics_interval_seconds": 120, "suppress_thread_spam": true, "detailed_memory_tracking": true, "alert_thresholds": {"cpu_warning": 90, "cpu_critical": 98, "memory_warning": 85, "memory_critical": 95, "thread_warning": 20, "thread_critical": 50}}, "large_tables": ["cef0.zscaler.nssweblog", "cloud.alibaba.log_service.events", "cloud.office365.management.exchange", "cloud.office365.management.securitycompliancecenter", "cloud.office365.management.endpoint", "cloud.office365.management.azureactivedirectory", "cloud.office365.management.sharepoint", "cloud.office365.management.onedrive", "firewall.fortinet.traffic.forward", "my.app.tngd.polardb", "netstat.zscaler.analyzer_zpa"], "table_specific_settings": {"cloud.office365.management.securitycompliancecenter": {"chunk_size": 2000, "timeout_seconds": 7200, "max_retries": 5, "memory_limit_mb": 200, "enable_streaming": true, "aggressive_chunking": true, "single_threaded": true, "gc_per_chunk": true}, "cloud.office365.management.azureactivedirectory": {"chunk_size": 3000, "timeout_seconds": 5400, "max_retries": 4, "memory_limit_mb": 250, "enable_streaming": true, "single_threaded": true}, "cloud.office365.management.exchange": {"chunk_size": 5000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 300}, "cef0.zscaler.nssweblog": {"chunk_size": 3000, "timeout_seconds": 5400, "max_retries": 4, "memory_limit_mb": 250}, "firewall.fortinet.traffic.forward": {"chunk_size": 5000, "timeout_seconds": 3600, "max_retries": 3, "memory_limit_mb": 300}}, "error_handling": {"max_consecutive_failures": 2, "failure_cooldown_minutes": 10, "auto_skip_problematic_tables": false, "detailed_error_logging": true, "error_notification_threshold": 2, "enable_table_isolation": true, "memory_leak_detection": true}, "recovery": {"checkpoint_enabled": true, "checkpoint_interval_minutes": 5, "auto_resume": true, "max_resume_attempts": 5, "resume_delay_minutes": 2, "granular_checkpoints": true}, "performance_optimizations": {"connection_pooling": false, "query_caching": false, "parallel_uploads": false, "memory_mapping": false, "compression_level": 3, "buffer_size_kb": 32, "single_table_mode": true, "conservative_memory_usage": true}, "logging": {"level": "INFO", "max_file_size_mb": 50, "backup_count": 10, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "separate_error_log": true, "log_rotation": true, "reduce_verbose_logging": true}}