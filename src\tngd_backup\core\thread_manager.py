#!/usr/bin/env python3
"""
Thread Manager Module

This module provides improved thread management for the TNGD backup system
to prevent "can't start new thread" errors and resource exhaustion.

Features:
- Thread pool management with limits
- Resource monitoring and throttling
- Automatic cleanup and garbage collection
- Thread health monitoring
"""

import threading
import time
import logging
import gc
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Optional
from dataclasses import dataclass
from contextlib import contextmanager

from ..constants import BackupConstants

logger = logging.getLogger(__name__)


@dataclass
class ThreadMetrics:
    """Thread usage metrics."""
    active_threads: int
    daemon_threads: int
    total_threads: int
    max_threads_reached: int
    thread_pool_size: int
    memory_usage_mb: float


class ThreadManager:
    """Enhanced thread manager with resource limits and monitoring."""
    
    def __init__(self, max_threads: int = BackupConstants.DEFAULT_MAX_THREADS, cleanup_interval: int = BackupConstants.DEFAULT_CLEANUP_INTERVAL_SECONDS):
        """
        Initialize thread manager.
        
        Args:
            max_threads: Maximum number of concurrent threads
            cleanup_interval: Cleanup interval in seconds
        """
        self.max_threads = max_threads
        self.cleanup_interval = cleanup_interval
        self.active_pools: Dict[str, ThreadPoolExecutor] = {}
        self.pool_lock = threading.Lock()
        self.metrics_lock = threading.Lock()
        
        # Metrics tracking
        self.max_threads_reached = 0
        self.cleanup_count = 0
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            name="thread-manager-cleanup",
            daemon=True
        )
        self.cleanup_thread.start()
        
        logger.info(f"ThreadManager initialized: max_threads={max_threads}")
    
    def get_metrics(self) -> ThreadMetrics:
        """Get current thread metrics."""
        with self.metrics_lock:
            active_count = threading.active_count()
            daemon_count = sum(1 for t in threading.enumerate() if t.daemon)
            
            # Get memory usage
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / (1024 * 1024)
            except ImportError:
                memory_mb = 0.0
            
            return ThreadMetrics(
                active_threads=active_count,
                daemon_threads=daemon_count,
                total_threads=active_count,
                max_threads_reached=self.max_threads_reached,
                thread_pool_size=len(self.active_pools),
                memory_usage_mb=memory_mb
            )
    
    @contextmanager
    def get_thread_pool(self, pool_name: str, max_workers: Optional[int] = None):
        """
        Get or create a thread pool with automatic cleanup.
        
        Args:
            pool_name: Unique name for the thread pool
            max_workers: Maximum workers (defaults to self.max_threads)
        """
        if max_workers is None:
            max_workers = min(self.max_threads, 4)  # Conservative default
        
        pool = None
        try:
            with self.pool_lock:
                if pool_name in self.active_pools:
                    pool = self.active_pools[pool_name]
                else:
                    # Check thread limits before creating new pool
                    current_threads = threading.active_count()
                    if current_threads + max_workers > self.max_threads * 2:
                        logger.warning(f"Thread limit approaching: {current_threads} active, "
                                     f"requested {max_workers} more")
                        # Force cleanup before creating new pool
                        self._force_cleanup()
                    
                    pool = ThreadPoolExecutor(
                        max_workers=max_workers,
                        thread_name_prefix=f"pool-{pool_name}"
                    )
                    self.active_pools[pool_name] = pool
                    
                    # Update metrics
                    if current_threads > self.max_threads_reached:
                        self.max_threads_reached = current_threads
            
            yield pool
            
        finally:
            # Don't shutdown immediately, let cleanup worker handle it
            pass
    
    def _cleanup_worker(self):
        """Background cleanup worker."""
        while True:
            try:
                time.sleep(self.cleanup_interval)
                self._perform_cleanup()
            except Exception as e:
                logger.error(f"Cleanup worker error: {e}")
    
    def _perform_cleanup(self):
        """Perform periodic cleanup."""
        with self.pool_lock:
            pools_to_remove = []
            
            for pool_name, pool in self.active_pools.items():
                # Check if pool is idle (no running tasks)
                if hasattr(pool, '_threads') and len(pool._threads) == 0:
                    pools_to_remove.append(pool_name)
            
            # Remove idle pools
            for pool_name in pools_to_remove:
                try:
                    pool = self.active_pools.pop(pool_name)
                    pool.shutdown(wait=False)
                    self.cleanup_count += 1
                    logger.debug(f"Cleaned up idle thread pool: {pool_name}")
                except Exception as e:
                    logger.error(f"Error cleaning up pool {pool_name}: {e}")
        
        # Force garbage collection
        gc.collect()
    
    def _force_cleanup(self):
        """Force immediate cleanup of all pools."""
        logger.info("Forcing immediate thread pool cleanup")

        with self.pool_lock:
            for pool_name, pool in list(self.active_pools.items()):
                try:
                    # More aggressive shutdown
                    pool.shutdown(wait=False)  # Don't wait, force immediate shutdown
                    del self.active_pools[pool_name]
                    logger.debug(f"Force cleaned pool: {pool_name}")
                except Exception as e:
                    logger.error(f"Error force cleaning pool {pool_name}: {e}")

        # Aggressive garbage collection
        gc.collect()
        time.sleep(1)  # Give system time to clean up

        # Log current thread count after cleanup
        current_threads = threading.active_count()
        logger.info(f"Thread count after force cleanup: {current_threads}")

    def emergency_shutdown(self):
        """Emergency shutdown of all thread pools - most aggressive cleanup."""
        logger.critical("EMERGENCY THREAD POOL SHUTDOWN INITIATED")

        try:
            with self.pool_lock:
                # Cancel all running tasks and shutdown immediately
                for pool_name, pool in list(self.active_pools.items()):
                    try:
                        # Cancel pending futures if possible
                        if hasattr(pool, '_threads'):
                            for thread in pool._threads:
                                if thread.is_alive():
                                    logger.warning(f"Terminating thread: {thread.name}")

                        pool.shutdown(wait=False)
                        del self.active_pools[pool_name]
                        logger.info(f"Emergency shutdown pool: {pool_name}")
                    except Exception as e:
                        logger.error(f"Error in emergency shutdown of pool {pool_name}: {e}")

                # Clear the pools dictionary
                self.active_pools.clear()

            # Multiple rounds of garbage collection
            for i in range(5):
                gc.collect()
                time.sleep(0.5)

            current_threads = threading.active_count()
            logger.critical(f"Emergency shutdown complete. Remaining threads: {current_threads}")

        except Exception as e:
            logger.error(f"Emergency shutdown failed: {e}")
    
    def shutdown(self):
        """Shutdown thread manager and all pools."""
        logger.info("Shutting down ThreadManager")
        
        with self.pool_lock:
            for pool_name, pool in self.active_pools.items():
                try:
                    pool.shutdown(wait=True, timeout=10)
                    logger.debug(f"Shutdown pool: {pool_name}")
                except Exception as e:
                    logger.error(f"Error shutting down pool {pool_name}: {e}")
            
            self.active_pools.clear()
        
        # Final cleanup
        gc.collect()


# Global thread manager instance
_thread_manager = None


def get_thread_manager() -> ThreadManager:
    """Get global thread manager instance."""
    global _thread_manager
    if _thread_manager is None:
        _thread_manager = ThreadManager()
    return _thread_manager


@contextmanager
def managed_thread_pool(pool_name: str, max_workers: Optional[int] = None):
    """Context manager for managed thread pools."""
    manager = get_thread_manager()
    with manager.get_thread_pool(pool_name, max_workers) as pool:
        yield pool


def log_thread_metrics():
    """Log current thread metrics."""
    manager = get_thread_manager()
    metrics = manager.get_metrics()
    
    logger.info(f"Thread Metrics: Active={metrics.active_threads}, "
                f"Daemon={metrics.daemon_threads}, "
                f"Pools={metrics.thread_pool_size}, "
                f"Memory={metrics.memory_usage_mb:.1f}MB")
