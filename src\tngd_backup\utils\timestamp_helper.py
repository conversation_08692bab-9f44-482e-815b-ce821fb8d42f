#!/usr/bin/env python3
"""
Timestamp Helper Utility

Simple utility to handle all timestamp conversions and date filtering
for the TNGD backup system. This provides easy-to-use functions for
converting between dates and timestamps.
"""

import logging
from datetime import datetime, timedelta
from typing import Tuple, Optional, Union
import pytz

logger = logging.getLogger(__name__)


class TimestampHelper:
    """Easy-to-use timestamp conversion utility."""
    
    def __init__(self, timezone: str = 'UTC'):
        """
        Initialize with timezone.
        
        Args:
            timezone: Timezone string (default: 'UTC')
        """
        self.timezone = pytz.timezone(timezone)
    
    def date_to_timestamp_range(self, date_str: str) -> Tuple[int, int]:
        """
        Convert a date string to start/end timestamps for the entire day.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Tuple of (start_timestamp_ms, end_timestamp_ms)
            
        Example:
            >>> helper = TimestampHelper()
            >>> start, end = helper.date_to_timestamp_range('2025-04-01')
            >>> print(f"Start: {start}, End: {end}")
        """
        try:
            # Parse the date
            target_date = datetime.strptime(date_str, '%Y-%m-%d')
            
            # Set timezone
            target_date = self.timezone.localize(target_date)
            
            # Start of day (00:00:00.000)
            start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # End of day (23:59:59.999)
            end_of_day = target_date.replace(hour=23, minute=59, second=59, microsecond=999000)
            
            # Convert to milliseconds timestamp
            start_timestamp = int(start_of_day.timestamp() * 1000)
            end_timestamp = int(end_of_day.timestamp() * 1000)
            
            logger.debug(f"Date {date_str} -> Timestamps: {start_timestamp} to {end_timestamp}")
            
            return start_timestamp, end_timestamp
            
        except ValueError as e:
            logger.error(f"Failed to parse date {date_str}: {e}")
            raise ValueError(f"Invalid date format: {date_str}. Use YYYY-MM-DD")
    
    def timestamp_to_date(self, timestamp: Union[int, float]) -> str:
        """
        Convert timestamp to date string.
        
        Args:
            timestamp: Timestamp in milliseconds or seconds
            
        Returns:
            Date string in YYYY-MM-DD format
        """
        try:
            # Auto-detect if timestamp is in seconds or milliseconds
            if timestamp > 1000000000000:  # Likely milliseconds
                dt = datetime.fromtimestamp(timestamp / 1000, tz=self.timezone)
            else:  # Likely seconds
                dt = datetime.fromtimestamp(timestamp, tz=self.timezone)
            
            return dt.strftime('%Y-%m-%d')
            
        except (ValueError, OSError) as e:
            logger.error(f"Failed to convert timestamp {timestamp}: {e}")
            return "Invalid timestamp"
    
    def create_flexible_where_clause(self, date_str: str) -> str:
        """
        Create a flexible WHERE clause that handles multiple date formats.

        Args:
            date_str: Date in YYYY-MM-DD format

        Returns:
            WHERE clause string that works with multiple formats
        """
        try:
            start_timestamp, end_timestamp = self.date_to_timestamp_range(date_str)

            # Use SIMPLE timestamp range only to avoid query parsing issues
            # The complex OR clause was causing unbalanced parentheses errors
            where_clause = f"eventdate >= {start_timestamp} and eventdate <= {end_timestamp}"

            logger.debug(f"Created simple WHERE clause for {date_str}: {where_clause}")
            return where_clause

        except ValueError:
            # Fallback to simple string comparison
            logger.warning(f"Using fallback string comparison for date: {date_str}")
            return f"eventdate = '{date_str}'"
    
    def create_simple_timestamp_where_clause(self, date_str: str) -> str:
        """
        Create a simple timestamp-based WHERE clause (recommended for performance).
        
        Args:
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Simple WHERE clause using timestamp range
        """
        try:
            start_timestamp, end_timestamp = self.date_to_timestamp_range(date_str)
            return f"eventdate >= {start_timestamp} and eventdate <= {end_timestamp}"
        except ValueError:
            return f"eventdate = '{date_str}'"
    
    def validate_date_string(self, date_str: str) -> bool:
        """
        Validate if a date string is in correct format.
        
        Args:
            date_str: Date string to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            datetime.strptime(date_str, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    def get_date_range_info(self, date_str: str) -> dict:
        """
        Get comprehensive information about a date range.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Dictionary with date range information
        """
        try:
            start_timestamp, end_timestamp = self.date_to_timestamp_range(date_str)
            
            start_dt = datetime.fromtimestamp(start_timestamp / 1000, tz=self.timezone)
            end_dt = datetime.fromtimestamp(end_timestamp / 1000, tz=self.timezone)
            
            return {
                'date': date_str,
                'start_timestamp': start_timestamp,
                'end_timestamp': end_timestamp,
                'start_datetime': start_dt.isoformat(),
                'end_datetime': end_dt.isoformat(),
                'timezone': str(self.timezone),
                'duration_hours': 24,
                'timestamp_range_ms': end_timestamp - start_timestamp
            }
        except ValueError as e:
            return {
                'date': date_str,
                'error': str(e),
                'valid': False
            }


# Convenience functions for easy usage
def get_timestamp_range(date_str: str, timezone: str = 'UTC') -> Tuple[int, int]:
    """
    Quick function to get timestamp range for a date.
    
    Args:
        date_str: Date in YYYY-MM-DD format
        timezone: Timezone string (default: 'UTC')
        
    Returns:
        Tuple of (start_timestamp_ms, end_timestamp_ms)
    """
    helper = TimestampHelper(timezone)
    return helper.date_to_timestamp_range(date_str)


def create_date_filter(date_str: str, simple: bool = True, timezone: str = 'UTC') -> str:
    """
    Quick function to create a date filter WHERE clause.
    
    Args:
        date_str: Date in YYYY-MM-DD format
        simple: If True, use simple timestamp range. If False, use flexible approach
        timezone: Timezone string (default: 'UTC')
        
    Returns:
        WHERE clause string
    """
    helper = TimestampHelper(timezone)
    
    if simple:
        return helper.create_simple_timestamp_where_clause(date_str)
    else:
        return helper.create_flexible_where_clause(date_str)


# Example usage
if __name__ == "__main__":
    # Example usage
    helper = TimestampHelper()
    
    # Test date
    test_date = "2025-04-01"
    
    print(f"Testing date: {test_date}")
    print(f"Valid date: {helper.validate_date_string(test_date)}")
    
    # Get timestamp range
    start, end = helper.date_to_timestamp_range(test_date)
    print(f"Timestamp range: {start} to {end}")
    
    # Create WHERE clauses
    simple_where = helper.create_simple_timestamp_where_clause(test_date)
    flexible_where = helper.create_flexible_where_clause(test_date)
    
    print(f"Simple WHERE: {simple_where}")
    print(f"Flexible WHERE: {flexible_where}")
    
    # Get detailed info
    info = helper.get_date_range_info(test_date)
    print(f"Date info: {info}")
