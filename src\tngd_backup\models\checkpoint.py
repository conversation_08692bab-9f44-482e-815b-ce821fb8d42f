#!/usr/bin/env python3
"""
Checkpoint and Progress Models

Data models for backup progress tracking, checkpointing, and recovery.
These models enable the backup system to resume from failures and track progress.
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum


class BackupStatus(Enum):
    """Backup status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TableStatus(Enum):
    """Table backup status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    NO_DATA = "no_data"
    SKIPPED = "skipped"


@dataclass
class TableProgress:
    """Progress information for a single table backup."""
    table_name: str
    status: TableStatus = TableStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    rows_processed: int = 0
    rows_total: Optional[int] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    file_size_mb: float = 0.0
    compression_ratio: float = 0.0
    upload_success: bool = False
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def progress_percent(self) -> Optional[float]:
        """Calculate progress percentage."""
        if self.rows_total and self.rows_total > 0:
            return (self.rows_processed / self.rows_total) * 100
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['status'] = self.status.value
        if self.start_time:
            data['start_time'] = self.start_time.isoformat()
        if self.end_time:
            data['end_time'] = self.end_time.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TableProgress':
        """Create from dictionary."""
        # Convert datetime strings back to datetime objects
        if 'start_time' in data and data['start_time']:
            data['start_time'] = datetime.fromisoformat(data['start_time'])
        if 'end_time' in data and data['end_time']:
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        
        # Convert status string to enum
        if 'status' in data:
            data['status'] = TableStatus(data['status'])
        
        return cls(**data)


@dataclass
class DateProgress:
    """Progress information for all tables in a specific date."""
    date: str  # YYYY-MM-DD format
    status: BackupStatus = BackupStatus.NOT_STARTED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    tables: Dict[str, TableProgress] = field(default_factory=dict)
    total_tables: int = 0
    completed_tables: int = 0
    failed_tables: int = 0
    total_rows: int = 0
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def progress_percent(self) -> float:
        """Calculate progress percentage."""
        if self.total_tables > 0:
            return (self.completed_tables / self.total_tables) * 100
        return 0.0
    
    def add_table(self, table_name: str) -> TableProgress:
        """Add a table to track."""
        table_progress = TableProgress(table_name=table_name)
        self.tables[table_name] = table_progress
        self.total_tables = len(self.tables)
        return table_progress
    
    def update_table_status(self, table_name: str, status: TableStatus, **kwargs):
        """Update table status and related information."""
        if table_name in self.tables:
            table = self.tables[table_name]
            table.status = status
            
            # Update additional fields
            for key, value in kwargs.items():
                if hasattr(table, key):
                    setattr(table, key, value)
            
            # Update counters
            self._update_counters()
    
    def _update_counters(self):
        """Update progress counters."""
        self.completed_tables = sum(1 for t in self.tables.values() 
                                  if t.status == TableStatus.COMPLETED)
        self.failed_tables = sum(1 for t in self.tables.values() 
                               if t.status == TableStatus.FAILED)
        self.total_rows = sum(t.rows_processed for t in self.tables.values())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'date': self.date,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'tables': {name: table.to_dict() for name, table in self.tables.items()},
            'total_tables': self.total_tables,
            'completed_tables': self.completed_tables,
            'failed_tables': self.failed_tables,
            'total_rows': self.total_rows
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DateProgress':
        """Create from dictionary."""
        # Convert datetime strings
        if data.get('start_time'):
            data['start_time'] = datetime.fromisoformat(data['start_time'])
        if data.get('end_time'):
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        
        # Convert status
        if 'status' in data:
            data['status'] = BackupStatus(data['status'])
        
        # Convert tables
        tables = {}
        for table_name, table_data in data.get('tables', {}).items():
            tables[table_name] = TableProgress.from_dict(table_data)
        data['tables'] = tables
        
        return cls(**data)


@dataclass
class BackupCheckpoint:
    """Main checkpoint data for backup operations."""
    backup_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: BackupStatus = BackupStatus.NOT_STARTED
    dates: List[str] = field(default_factory=list)  # YYYY-MM-DD format
    tables: List[str] = field(default_factory=list)
    date_progress: Dict[str, DateProgress] = field(default_factory=dict)
    current_date: Optional[str] = None
    current_table: Optional[str] = None
    total_operations: int = 0
    completed_operations: int = 0
    failed_operations: int = 0
    total_rows_processed: int = 0
    error_message: Optional[str] = None
    
    # Legacy fields for backward compatibility
    completed_tables: Dict[str, List[str]] = field(default_factory=dict)
    failed_tables: Dict[str, List[str]] = field(default_factory=dict)
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Calculate total duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def progress_percent(self) -> float:
        """Calculate overall progress percentage."""
        if self.total_operations > 0:
            return (self.completed_operations / self.total_operations) * 100
        return 0.0
    
    def initialize_dates(self, dates: List[str], tables: List[str]):
        """Initialize checkpoint with dates and tables."""
        self.dates = dates
        self.tables = tables
        self.total_operations = len(dates) * len(tables)
        
        # Initialize date progress
        for date in dates:
            date_progress = DateProgress(date=date)
            for table in tables:
                date_progress.add_table(table)
            self.date_progress[date] = date_progress
    
    def start_date(self, date: str):
        """Mark start of date processing."""
        self.current_date = date
        if date in self.date_progress:
            self.date_progress[date].status = BackupStatus.IN_PROGRESS
            self.date_progress[date].start_time = datetime.now()
    
    def complete_date(self, date: str):
        """Mark completion of date processing."""
        if date in self.date_progress:
            date_prog = self.date_progress[date]
            date_prog.status = BackupStatus.COMPLETED
            date_prog.end_time = datetime.now()
            date_prog._update_counters()
        
        if date == self.current_date:
            self.current_date = None
    
    def start_table(self, date: str, table: str):
        """Mark start of table processing."""
        self.current_table = table
        if date in self.date_progress and table in self.date_progress[date].tables:
            table_prog = self.date_progress[date].tables[table]
            table_prog.status = TableStatus.PROCESSING
            table_prog.start_time = datetime.now()
    
    def complete_table(self, date: str, table: str, rows: int = 0, **kwargs):
        """Mark completion of table processing."""
        if date in self.date_progress and table in self.date_progress[date].tables:
            table_prog = self.date_progress[date].tables[table]
            table_prog.status = TableStatus.COMPLETED
            table_prog.end_time = datetime.now()
            table_prog.rows_processed = rows
            
            # Update additional fields
            for key, value in kwargs.items():
                if hasattr(table_prog, key):
                    setattr(table_prog, key, value)
            
            self.date_progress[date]._update_counters()
            self._update_overall_counters()
        
        if table == self.current_table:
            self.current_table = None
    
    def fail_table(self, date: str, table: str, error: str, **kwargs):
        """Mark table as failed."""
        if date in self.date_progress and table in self.date_progress[date].tables:
            table_prog = self.date_progress[date].tables[table]
            table_prog.status = TableStatus.FAILED
            table_prog.end_time = datetime.now()
            table_prog.error_message = error
            
            # Update additional fields
            for key, value in kwargs.items():
                if hasattr(table_prog, key):
                    setattr(table_prog, key, value)
            
            self.date_progress[date]._update_counters()
            self._update_overall_counters()
    
    def _update_overall_counters(self):
        """Update overall progress counters."""
        self.completed_operations = sum(dp.completed_tables for dp in self.date_progress.values())
        self.failed_operations = sum(dp.failed_tables for dp in self.date_progress.values())
        self.total_rows_processed = sum(dp.total_rows for dp in self.date_progress.values())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'backup_id': self.backup_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status.value,
            'dates': self.dates,
            'tables': self.tables,
            'date_progress': {date: prog.to_dict() for date, prog in self.date_progress.items()},
            'current_date': self.current_date,
            'current_table': self.current_table,
            'total_operations': self.total_operations,
            'completed_operations': self.completed_operations,
            'failed_operations': self.failed_operations,
            'total_rows_processed': self.total_rows_processed,
            'error_message': self.error_message,
            # Legacy fields
            'completed_tables': self.completed_tables,
            'failed_tables': self.failed_tables
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BackupCheckpoint':
        """Create from dictionary."""
        # Convert datetime strings
        data['start_time'] = datetime.fromisoformat(data['start_time'])
        if data.get('end_time'):
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        
        # Convert status
        if 'status' in data:
            data['status'] = BackupStatus(data['status'])
        
        # Convert date progress
        date_progress = {}
        for date, prog_data in data.get('date_progress', {}).items():
            date_progress[date] = DateProgress.from_dict(prog_data)
        data['date_progress'] = date_progress
        
        return cls(**data)
