#!/usr/bin/env python3
"""
Simple test script to pull raw data from Devo tables for verification.
This script will query all tables for a specific date and show the actual data retrieved.
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from tngd_backup.core.devo_client import DevoClient
except ImportError as e:
    print(f"Error importing DevoClient: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DevoRawDataTester:
    """Simple tester to pull raw data from Devo tables."""
    
    def __init__(self):
        """Initialize the tester."""
        self.devo_client = None
        self.tables = []
        self.results = {}
        
    def load_tables(self) -> List[str]:
        """Load table list from config."""
        try:
            with open('config/tables.json', 'r') as f:
                tables = json.load(f)
            logger.info(f"Loaded {len(tables)} tables from config")
            return tables
        except Exception as e:
            logger.error(f"Failed to load tables config: {e}")
            return []
    
    def initialize_client(self) -> bool:
        """Initialize Devo client."""
        try:
            self.devo_client = DevoClient()
            logger.info("Devo client initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Devo client: {e}")
            return False
    
    def test_single_table(self, table_name: str, target_date: str, limit: int = 10) -> Dict[str, Any]:
        """
        Test a single table and return results.
        
        Args:
            table_name: Name of the table to query
            target_date: Date to query (YYYY-MM-DD format)
            limit: Maximum number of rows to retrieve
            
        Returns:
            Dictionary with test results
        """
        result = {
            'table': table_name,
            'status': 'unknown',
            'row_count': 0,
            'data': [],
            'error': None,
            'query_time': None
        }
        
        try:
            # Create simple query
            query = f"from {table_name} select * limit {limit}"
            
            # Calculate date range (target date + 1 day)
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            from_date = target_dt.strftime('%Y-%m-%d')
            to_date = (target_dt + timedelta(days=1)).strftime('%Y-%m-%d')
            
            logger.info(f"Querying {table_name} for date {from_date}")
            
            start_time = datetime.now()
            
            # Execute query with specific date range
            data = self.devo_client.execute_query(
                query=query,
                from_date=f"'{from_date}'",
                timeout=60,
                table_name=table_name
            )
            
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            if data is not None:
                result['status'] = 'success'
                result['row_count'] = len(data)
                result['data'] = data[:5]  # Store first 5 rows for inspection
                result['query_time'] = query_time
                
                if len(data) > 0:
                    logger.info(f"✅ {table_name}: {len(data)} rows retrieved in {query_time:.2f}s")
                else:
                    logger.info(f"⚠️  {table_name}: No data found for {from_date}")
            else:
                result['status'] = 'no_data'
                logger.warning(f"❌ {table_name}: Query returned None")
                
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            logger.error(f"❌ {table_name}: Error - {str(e)}")
            
        return result
    
    def test_all_tables(self, target_date: str, limit: int = 10) -> Dict[str, Any]:
        """
        Test all tables for the given date.
        
        Args:
            target_date: Date to query (YYYY-MM-DD format)
            limit: Maximum number of rows per table
            
        Returns:
            Dictionary with all results
        """
        if not self.devo_client:
            logger.error("Devo client not initialized")
            return {}
            
        if not self.tables:
            self.tables = self.load_tables()
            
        if not self.tables:
            logger.error("No tables to test")
            return {}
            
        logger.info(f"Testing {len(self.tables)} tables for date {target_date}")
        logger.info("=" * 60)
        
        results = {
            'test_date': target_date,
            'total_tables': len(self.tables),
            'successful': 0,
            'no_data': 0,
            'errors': 0,
            'tables': {}
        }
        
        for i, table in enumerate(self.tables, 1):
            logger.info(f"[{i}/{len(self.tables)}] Testing {table}")
            
            result = self.test_single_table(table, target_date, limit)
            results['tables'][table] = result
            
            # Update counters
            if result['status'] == 'success':
                if result['row_count'] > 0:
                    results['successful'] += 1
                else:
                    results['no_data'] += 1
            else:
                results['errors'] += 1
        
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print a summary of the test results."""
        if not results:
            logger.error("No results to summarize")
            return
            
        logger.info("=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Test Date: {results['test_date']}")
        logger.info(f"Total Tables: {results['total_tables']}")
        logger.info(f"✅ Successful (with data): {results['successful']}")
        logger.info(f"⚠️  No data found: {results['no_data']}")
        logger.info(f"❌ Errors: {results['errors']}")
        logger.info("")
        
        # Show tables with data
        tables_with_data = []
        tables_no_data = []
        tables_with_errors = []
        
        for table, result in results['tables'].items():
            if result['status'] == 'success' and result['row_count'] > 0:
                tables_with_data.append((table, result['row_count']))
            elif result['status'] == 'success' and result['row_count'] == 0:
                tables_no_data.append(table)
            else:
                tables_with_errors.append((table, result.get('error', 'Unknown error')))
        
        if tables_with_data:
            logger.info("TABLES WITH DATA:")
            for table, count in sorted(tables_with_data, key=lambda x: x[1], reverse=True):
                logger.info(f"  • {table}: {count} rows")
            logger.info("")
        
        if tables_no_data:
            logger.info("TABLES WITH NO DATA:")
            for table in sorted(tables_no_data):
                logger.info(f"  • {table}")
            logger.info("")
        
        if tables_with_errors:
            logger.info("TABLES WITH ERRORS:")
            for table, error in sorted(tables_with_errors):
                logger.info(f"  • {table}: {error}")
            logger.info("")
    
    def save_results(self, results: Dict[str, Any], filename: Optional[str] = None):
        """Save results to JSON file."""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"devo_test_results_{results['test_date']}_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            logger.info(f"Results saved to {filename}")
        except Exception as e:
            logger.error(f"Failed to save results: {e}")

def main():
    """Main function."""
    # Default test date (the same date from your backup)
    test_date = "2025-04-08"
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        test_date = sys.argv[1]
    
    # Validate date format
    try:
        datetime.strptime(test_date, '%Y-%m-%d')
    except ValueError:
        logger.error(f"Invalid date format: {test_date}. Use YYYY-MM-DD format.")
        sys.exit(1)
    
    logger.info(f"Starting Devo raw data test for date: {test_date}")
    
    # Initialize tester
    tester = DevoRawDataTester()
    
    # Initialize Devo client
    if not tester.initialize_client():
        logger.error("Failed to initialize Devo client. Check your credentials.")
        sys.exit(1)
    
    # Load tables
    tester.tables = tester.load_tables()
    if not tester.tables:
        logger.error("No tables loaded. Check config/tables.json")
        sys.exit(1)
    
    # Run tests
    results = tester.test_all_tables(test_date, limit=10)
    
    # Print summary
    tester.print_summary(results)
    
    # Save results
    tester.save_results(results)
    
    logger.info("Test completed!")

if __name__ == "__main__":
    main()
