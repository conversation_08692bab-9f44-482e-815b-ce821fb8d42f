#!/usr/bin/env python3
"""
Test script to verify logging configuration is working properly.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_backup_engine_logging():
    """Test the BackupEngine logging setup."""
    print("Testing BackupEngine logging...")
    
    try:
        from tngd_backup.core.backup_engine import BackupEngine
        
        # Create a backup engine instance
        engine = BackupEngine(dates=[datetime.now()])
        
        # Test logging
        engine.logger.info("Test log message from BackupEngine")
        engine.logger.warning("Test warning message")
        engine.logger.error("Test error message")
        
        print(f"[OK] BackupEngine logging working. Log file: {engine.log_file_path}")
        
        # Check if log file exists and has content
        log_file = Path(engine.log_file_path)
        if log_file.exists():
            with open(log_file, 'r') as f:
                content = f.read()
                if content.strip():
                    print(f"[OK] Log file has content ({len(content)} characters)")
                    print("Last few lines:")
                    lines = content.strip().split('\n')
                    for line in lines[-3:]:
                        print(f"  {line}")
                else:
                    print("[ERROR] Log file is empty")
        else:
            print("[ERROR] Log file does not exist")
            
        return True
        
    except Exception as e:
        print(f"[ERROR] BackupEngine logging test failed: {e}")
        return False

def test_resource_monitor_logging():
    """Test the ResourceMonitor logging setup."""
    print("\nTesting ResourceMonitor logging...")
    
    try:
        from tngd_backup.utils.monitoring import ResourceMonitor
        
        # Create a resource monitor instance
        monitor = ResourceMonitor()
        
        # Test logging
        monitor.logger.info("Test log message from ResourceMonitor")
        monitor.logger.warning("Test warning message")
        monitor.logger.error("Test error message")
        
        print(f"[OK] ResourceMonitor logging working. Log file: {monitor.log_file}")
        
        # Check if log file exists and has content
        log_file = Path(monitor.log_file)
        if log_file.exists():
            with open(log_file, 'r') as f:
                content = f.read()
                if content.strip():
                    print(f"[OK] Log file has content ({len(content)} characters)")
                    print("Last few lines:")
                    lines = content.strip().split('\n')
                    for line in lines[-3:]:
                        print(f"  {line}")
                else:
                    print("[ERROR] Log file is empty")
        else:
            print("[ERROR] Log file does not exist")
            
        return True
        
    except Exception as e:
        print(f"[ERROR] ResourceMonitor logging test failed: {e}")
        return False

def test_system_health():
    """Test system health monitoring."""
    print("\nTesting system health monitoring...")
    
    try:
        from tngd_backup.utils.monitoring import ResourceMonitor
        
        monitor = ResourceMonitor()
        health = monitor.get_system_health()
        
        print(f"[OK] System Health:")
        print(f"  CPU: {health.cpu_percent:.1f}%")
        print(f"  Memory: {health.memory_percent:.1f}%")
        print(f"  Threads: {health.thread_count}")
        print(f"  Status: {health.status}")
        
        if health.thread_count > 100:
            print(f"[WARNING] High thread count detected: {health.thread_count}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] System health test failed: {e}")
        return False

def main():
    """Main test function."""
    print("=== TNGD Backup System Logging Test ===")
    print(f"Test started at: {datetime.now()}")
    
    # Run tests
    tests = [
        test_backup_engine_logging,
        test_resource_monitor_logging,
        test_system_health
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"[ERROR] Test {test.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("[OK] All tests passed! Logging is working correctly.")
        return 0
    else:
        print("[ERROR] Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
