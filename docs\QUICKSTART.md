# 🚀 TNGD Backup System - Quick Start

## Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

## Configuration

Set up your environment variables for:
- OSS credentials (for cloud storage)
- Devo API access credentials

## Running Backups

### Basic Commands

```bash
# Backup today's data
python scripts/run_backup.py

# Backup specific date
python scripts/run_backup.py 2025-03-26

# Backup date range
python scripts/run_backup.py 2025-03-26 2025-03-31

# Use production configuration
python scripts/run_backup.py --config production 2025-03-26
```

### Monitoring

```bash
# Monitor backup progress
python scripts/monitor_progress.py

# System resource monitoring
python -m tngd_backup.utils.monitoring --monitor

# Generate maintenance report
python scripts/system_maintenance.py --report
```

## Testing

```bash
# Run unit tests
python -m pytest tests/unit/

# Run integration tests
python -m pytest tests/integration/

# Run all tests with coverage
python -m pytest tests/ --cov=src/tngd_backup
```

## Key Features

✅ **Advanced Resource Management** - Thread pooling, memory optimization
✅ **Real-time Monitoring** - System health monitoring with alerts
✅ **Automatic Recovery** - Checkpoint system with resume capability
✅ **Streaming Processing** - Efficient handling of large datasets
✅ **Professional Architecture** - Clean, maintainable, scalable codebase

## Performance

- **Memory Usage**: ~60MB (highly optimized)
- **Thread Count**: 4-6 controlled threads
- **Error Rate**: Near zero with comprehensive error handling
- **Recovery**: Automatic resume from checkpoints

---

For detailed documentation, see [README.md](README.md)
