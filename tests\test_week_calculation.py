#!/usr/bin/env python3
"""
Test script to verify week calculation for different dates
"""

from datetime import datetime
import calendar

def test_week_calculation():
    """Test week calculation for various dates."""
    
    print("=== Testing Week Calculation for OSS Path ===")
    
    # Test dates
    test_dates = [
        datetime(2025, 4, 14),  # Your current backup
        datetime(2025, 4, 16),  # Your question
        datetime(2025, 4, 1),   # First day of April
        datetime(2025, 4, 7),   # End of week 1
        datetime(2025, 4, 8),   # Start of week 2
        datetime(2025, 4, 15),  # End of week 2
        datetime(2025, 4, 22),  # Start of week 4
        datetime(2025, 4, 30),  # Last day of April
    ]
    
    print("Week breakdown for April 2025:")
    print("Date       | Day | Week | OSS Path")
    print("-" * 60)
    
    for target_date in test_dates:
        table_name = 'my.app.tngd.actiontraillinux'
        
        # Generate filename with date
        date_str = target_date.strftime('%Y-%m-%d')
        filename = f"{table_name.replace('.', '_')}_{date_str}.tar.gz"
        
        # Calculate week of month
        week_of_month = ((target_date.day - 1) // 7) + 1
        
        # Generate path parts
        path_parts = []
        path_parts.append('Devo')
        path_parts.append(calendar.month_name[target_date.month])  # April
        path_parts.append(f'week {week_of_month}')
        path_parts.append(date_str)
        path_parts.append(filename)
        
        upload_path = '/'.join(path_parts)
        full_path = f"oss://siem-security-logs/{upload_path}"
        
        print(f"{target_date.strftime('%Y-%m-%d')} | {target_date.day:2d}  | {week_of_month}    | {path_parts[1]}/{path_parts[2]}/{path_parts[3]}/")
    
    # Specific answer for your question
    print("\n" + "=" * 60)
    target_date = datetime(2025, 4, 16)
    date_str = target_date.strftime('%Y-%m-%d')
    week_of_month = ((target_date.day - 1) // 7) + 1
    filename = f"my_app_tngd_actiontraillinux_{date_str}.tar.gz"
    
    full_path = f"oss://siem-security-logs/Devo/April/week {week_of_month}/{date_str}/{filename}"
    
    print(f"✅ YES! For backup date 2025-04-16:")
    print(f"   OSS path will be: {full_path}")
    print(f"   Week calculation: April 16 = day 16 -> week {week_of_month}")

if __name__ == "__main__":
    test_week_calculation()
