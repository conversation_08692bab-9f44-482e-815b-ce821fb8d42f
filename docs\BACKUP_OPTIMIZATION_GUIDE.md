# 🚀 TNGD Backup System - Large Dataset Optimization Guide

## 📋 Overview

This guide explains the optimizations made to handle large datasets better, particularly for problematic tables like `cloud.office365.management.securitycompliancecenter`.

## 🔍 Issues Identified

### 1. **Thread Explosion**
- **Problem**: System created 1,549+ threads (far exceeding the 4 max_threads setting)
- **Impact**: Resource exhaustion, system instability
- **Solution**: Reduced max_threads to 1-2, added thread monitoring

### 2. **Missing Table Configuration**
- **Problem**: `cloud.office365.management.securitycompliancecenter` not in large_tables list
- **Impact**: Used default settings instead of optimized large table settings
- **Solution**: Added to large_tables with specific conservative settings

### 3. **Excessive Logging**
- **Problem**: Thread metrics logged every few milliseconds
- **Impact**: Log spam, I/O overhead
- **Solution**: Increased logging intervals, added spam suppression

### 4. **Resource Thresholds**
- **Problem**: Settings too aggressive for large datasets
- **Impact**: Memory pressure, timeouts
- **Solution**: Conservative memory limits, longer timeouts

## 🛠️ Configuration Files

### 1. **config/config.json** (Optimized Default)
- **Threads**: 2 (reduced from 4)
- **Memory**: 2000MB threshold (increased from 1500MB)
- **Chunk Size**: 10,000 (reduced from 20,000)
- **Timeouts**: 1800s default, 3600s for large tables

### 2. **config/large_dataset.json** (Conservative)
- **Threads**: 1 (single-threaded for stability)
- **Memory**: 3000MB threshold
- **Chunk Size**: 5,000 (very conservative)
- **Timeouts**: 3600s default, 7200s for large tables
- **Special**: Aggressive memory management, forced streaming

### 3. **Key Optimizations Applied**

```json
{
  "cloud.office365.management.securitycompliancecenter": {
    "chunk_size": 2000,              // Very small chunks
    "timeout_seconds": 7200,         // 2 hour timeout
    "max_retries": 5,                // More retry attempts
    "memory_limit_mb": 200,          // Conservative memory
    "single_threaded": true,         // No parallelism
    "gc_per_chunk": true            // Garbage collect per chunk
  }
}
```

## 🎯 Usage Instructions

### 1. **Apply Optimized Configuration**
```bash
# Interactive configuration selector
python scripts/optimize_config.py

# Or apply specific configuration
python scripts/optimize_config.py large_dataset
```

### 2. **Resume Failed Backup**
```bash
# Analyze progress and resume from where it left off
python scripts/resume_backup.py
```

### 3. **Monitor Backup Progress**
```bash
# Real-time monitoring of resources and progress
python scripts/monitor_backup.py
```

## 📊 Configuration Comparison

| Configuration | Threads | Memory | Chunk Size | Best For |
|---------------|---------|--------|------------|----------|
| default       | 4       | 1500MB | 20,000     | Normal use |
| optimized     | 2       | 2000MB | 10,000     | Balanced |
| large_dataset | 1       | 3000MB | 5,000      | Problem data |
| production    | 6       | 4000MB | 25,000     | High-end systems |

## 🔧 Key Optimizations Explained

### 1. **Thread Management**
- **Single-threaded mode** for problematic tables
- **Conservative thread limits** to prevent explosion
- **Automatic cleanup** and garbage collection

### 2. **Memory Management**
- **Smaller chunk sizes** to reduce memory pressure
- **Aggressive garbage collection** after each chunk
- **Memory monitoring** with automatic throttling

### 3. **Timeout Handling**
- **Progressive timeouts** based on table size
- **Longer timeouts** for known large tables
- **Query cancellation** support

### 4. **Error Handling**
- **More retry attempts** for large tables
- **Detailed error logging** for debugging
- **Table isolation** to prevent cascade failures

## 🚨 Troubleshooting

### If Backup Still Fails:

1. **Check System Resources**
   ```bash
   python scripts/monitor_backup.py
   ```

2. **Use Most Conservative Settings**
   ```bash
   python scripts/optimize_config.py large_dataset
   ```

3. **Process Single Dates**
   ```bash
   python run_backup.py --config config/large_dataset.json 2025-04-23
   ```

4. **Skip Problematic Tables Temporarily**
   - Edit `config/tables.json` to remove problematic tables
   - Process them separately later

### Common Issues:

- **Memory errors**: Reduce chunk_size further (try 1000)
- **Timeout errors**: Increase timeout_seconds (try 10800)
- **Thread errors**: Ensure max_threads is 1
- **Connection errors**: Reduce connection_pool_size to 1

## 📈 Performance Tips

1. **Start Small**: Test with single dates first
2. **Monitor Resources**: Use the monitoring script
3. **Gradual Scaling**: Increase settings only if stable
4. **Regular Cleanup**: Clear temp files between runs

## 🔄 Recovery Process

1. **Analyze Progress**: `python scripts/resume_backup.py`
2. **Apply Conservative Config**: Use `large_dataset.json`
3. **Resume from Last Date**: Script will calculate remaining dates
4. **Monitor Closely**: Watch for resource issues

## 📝 Next Steps

1. **Test the optimized configuration** with a single date
2. **Monitor resource usage** during backup
3. **Adjust settings** based on your system's performance
4. **Resume the failed backup** with remaining dates

## 🆘 Emergency Procedures

If backup fails again:
1. **Kill any stuck processes**
2. **Clear temp files**: `rm -rf data/temp/*`
3. **Use single-date mode**: Process one date at a time
4. **Contact support** with log files if issues persist
