# TNGD Backup System v2.0 - Environment Variables Template
# Copy this file to .env and fill in your actual values

# Devo API Configuration
DEVO_API_KEY=your_devo_api_key_here
DEVO_API_SECRET=your_devo_api_secret_here
DEVO_DOMAIN=your_devo_domain_here
DEVO_OAUTH_TOKEN=your_oauth_token_here

# OSS Storage Configuration
OSS_ACCESS_KEY_ID=your_oss_access_key_here
OSS_ACCESS_KEY_SECRET=your_oss_secret_key_here
OSS_BUCKET_NAME=your_bucket_name_here
OSS_ENDPOINT=your_oss_endpoint_here
OSS_REGION=your_oss_region_here

# Email Configuration (Optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
EMAIL_ENABLED=true

# Backup Configuration
BACKUP_ENVIRONMENT=default
LOG_LEVEL=INFO
ENABLE_MONITORING=true
ENABLE_CHECKPOINTS=true

# Security Settings
ENCRYPT_CREDENTIALS=false
CREDENTIAL_STORE_PATH=./credentials/

# Performance Tuning
MAX_THREADS=4
MEMORY_THRESHOLD_MB=1500
CHUNK_SIZE=20000
STREAMING_THRESHOLD=50000



# Monitoring and Alerts
ENABLE_RESOURCE_MONITORING=true
ALERT_CPU_THRESHOLD=80
ALERT_MEMORY_THRESHOLD=75
HEALTH_CHECK_INTERVAL=300

# Paths (relative to project root)
CONFIG_PATH=config/default.json
TABLES_PATH=config/tables.json
LOG_PATH=data/logs/
CHECKPOINT_PATH=data/checkpoints/
TEMP_PATH=data/temp/
