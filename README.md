# TNGD Backup System

A professional, enterprise-grade backup system for TNGD data with advanced resource management, monitoring, and recovery capabilities.

## 🚀 Features

- **Advanced Resource Management**: Thread pooling, memory optimization, and CPU throttling
- **Real-time Monitoring**: System health monitoring with configurable alerts
- **Automatic Recovery**: Checkpoint system with resume capability
- **Streaming Processing**: Efficient handling of large datasets
- **Comprehensive Error Handling**: Retry logic with exponential backoff
- **OSS Integration**: Secure cloud storage with compression
- **Professional Architecture**: Clean, maintainable, and scalable codebase

## 📋 Quick Start

### Prerequisites

- Python 3.7+
- Required Python packages (see `requirements.txt`)
- OSS credentials configured
- Devo API access

### Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Configure environment variables (set OSS credentials and Devo API access)
# Set environment variables for authentication
```

### Basic Usage

```bash
# Backup today's data
python scripts/run_backup.py

# Backup specific date
python scripts/run_backup.py 2025-03-26

# Backup date range
python scripts/run_backup.py 2025-03-26 2025-03-31

# Use production configuration
python scripts/run_backup.py --config production 2025-03-26
```

## 📁 Project Structure

```
TNGD/
├── src/tngd_backup/          # Main source code
│   ├── core/                 # Core business logic
│   ├── utils/                # Utility modules
│   └── models/               # Data models
├── config/                   # Configuration files
├── scripts/                  # Executable scripts
├── tests/                    # Test suite
├── docs/                     # Documentation
├── data/                     # Runtime data (logs, temp)
└── deployment/               # Deployment files
```

## 🔧 Configuration

The system supports multiple configuration environments:

- **`config/default.json`**: Default settings for general use
- **`config/production.json`**: Optimized for production environments


## 📊 Monitoring

### Real-time Monitoring

```bash
# Monitor backup progress
python scripts/monitor_progress.py

# System resource monitoring
python -m tngd_backup.utils.monitoring --monitor

# Generate maintenance report
python scripts/system_maintenance.py --report
```

## 🔄 Recovery

The system includes automatic recovery capabilities:

- **Checkpoint System**: Progress saved every 15 minutes
- **Auto-resume**: Automatic restart from last checkpoint
- **Failure Handling**: Intelligent retry with backoff

## 📈 Performance

### Optimized Resource Usage

- **Memory Usage**: ~60MB (optimized from previous 3000MB+ usage)
- **Thread Count**: 4-6 controlled threads (prevents resource exhaustion)
- **Error Rate**: Near zero with comprehensive error handling
- **Recovery**: Automatic resume from checkpoints

## 🧪 Testing

```bash
# Run unit tests
python -m pytest tests/unit/

# Run integration tests
python -m pytest tests/integration/

# Run all tests with coverage
python -m pytest tests/ --cov=src/tngd_backup
```

## 📚 Documentation

- **[Installation Guide](docs/installation.md)**: Detailed setup instructions
- **[Configuration Guide](docs/configuration.md)**: Complete configuration reference
- **[Usage Guide](docs/usage.md)**: Comprehensive usage examples
- **[Troubleshooting](docs/troubleshooting.md)**: Common issues and solutions

---

**TNGD Backup System** - Professional, Reliable, Scalable
