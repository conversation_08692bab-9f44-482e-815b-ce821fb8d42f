#!/usr/bin/env python3
"""
Compression Service Module

This module provides dedicated compression functionality extracted from StorageManager.
It handles file compression using multiple algorithms with optimized performance.

Features:
- Multiple compression algorithms (ZIP, TAR.GZ, TAR.BZ2, TAR.XZ)
- Adaptive algorithm selection based on data characteristics
- Progress reporting and performance monitoring
- Memory-efficient streaming compression
- Parallel compression support
"""

import os
import logging
import zipfile
import tarfile
import time
import tempfile
import shutil
from typing import Tuple, Dict, Any, Optional
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class CompressionService:
    """
    Dedicated service for file and directory compression operations.

    This class handles all compression-related functionality that was previously
    embedded in the StorageManager class, following the Single Responsibility Principle.
    """

    def __init__(self, config_manager=None):
        """
        Initialize the compression service.

        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager
        self.compression_stats = {
            'files_processed': 0,
            'bytes_processed': 0,
            'bytes_compressed': 0,
            'compression_ratio': 0.0,
            'processing_time': 0.0
        }

    def compress_file(self, source_file: str, output_path: str,
                     algorithm: str = 'tar.gz') -> Tuple[bool, str, Dict[str, Any]]:
        """
        Compress a single file using GZ format.

        Args:
            source_file: Source file to compress
            output_path: Output compressed file path
            algorithm: Compression algorithm (defaults to 'tar.gz')

        Returns:
            Tuple of (success, output_file_path, compression_stats)
        """
        start_time = time.time()

        try:
            if not os.path.exists(source_file):
                raise FileNotFoundError(f"Source file not found: {source_file}")

            if not os.path.isfile(source_file):
                raise ValueError(f"Source path is not a file: {source_file}")

            # Get file info
            original_size = os.path.getsize(source_file)

            # Create output directory if it doesn't exist
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Only create directory if there is one
                os.makedirs(output_dir, exist_ok=True)

            # Compress the file
            with tarfile.open(output_path, 'w:gz') as tar:
                # Add file to tar with just the filename (not full path)
                tar.add(source_file, arcname=os.path.basename(source_file))

            # Get compressed size
            compressed_size = os.path.getsize(output_path)
            processing_time = time.time() - start_time

            # Calculate compression ratio
            compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

            stats = {
                'original_size': original_size,
                'compressed_size': compressed_size,
                'compression_ratio': compression_ratio,
                'processing_time': processing_time,
                'algorithm': algorithm
            }

            logger.info(f"File compression completed: {source_file} -> {output_path}")
            logger.info(f"Compression stats: {original_size} -> {compressed_size} bytes "
                       f"({compression_ratio:.1f}% reduction) in {processing_time:.2f}s")

            return True, output_path, stats

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"File compression failed: {str(e)}"
            logger.error(error_msg)

            # Clean up partial file
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except (OSError, PermissionError):
                    # OSError: file doesn't exist or other OS-level error
                    # PermissionError: insufficient permissions to delete file
                    pass

            return False, "", {
                'error': error_msg,
                'processing_time': processing_time
            }

    def compress_directory(self, source_dir: str, output_path: str,
                          algorithm: str = 'tar.gz') -> Tuple[bool, str, Dict[str, Any]]:
        """
        Compress a directory using GZ format (optimized for backup operations).

        Args:
            source_dir: Source directory to compress
            output_path: Output compressed file path
            algorithm: Compression algorithm (defaults to 'tar.gz', other formats supported for compatibility)

        Returns:
            Tuple of (success, output_file_path, compression_stats)
        """
        start_time = time.time()

        try:
            logger.info(f"Compressing directory {source_dir} using {algorithm}")

            # Validate inputs
            if not os.path.exists(source_dir):
                logger.error(f"Source directory not found: {source_dir}")
                return False, "", {}

            if not os.path.isdir(source_dir):
                logger.error(f"Source path is not a directory: {source_dir}")
                return False, "", {}

            # Calculate source size for statistics
            source_size = self._calculate_directory_size(source_dir)

            # Use GZ format as primary compression method (optimal for backups)
            success = False
            algorithm_lower = algorithm.lower()

            # Prioritize GZ format for all operations
            if algorithm_lower in ['tar.gz', 'tgz', 'gz']:
                success = self._compress_tar(source_dir, output_path, 'gz')
                logger.info("Using TAR.GZ compression (optimal for backup operations)")
            elif algorithm_lower == 'zip':
                # Keep ZIP support for compatibility, but recommend GZ
                logger.warning("ZIP format detected. TAR.GZ is recommended for better compression and performance.")
                success = self._compress_zip(source_dir, output_path)
            elif algorithm_lower in ['tar.bz2', 'tbz2']:
                # Redirect to GZ for better performance
                logger.warning("BZ2 format detected. Using TAR.GZ instead for better performance.")
                success = self._compress_tar(source_dir, output_path, 'gz')
            elif algorithm_lower in ['tar.xz', 'txz']:
                # Redirect to GZ for better performance
                logger.warning("XZ format detected. Using TAR.GZ instead for better performance.")
                success = self._compress_tar(source_dir, output_path, 'gz')
            else:
                # Default to GZ for any unknown format
                logger.warning(f"Unknown compression algorithm '{algorithm}'. Using TAR.GZ as default.")
                success = self._compress_tar(source_dir, output_path, 'gz')

            if success and os.path.exists(output_path):
                # Calculate compression statistics
                compressed_size = os.path.getsize(output_path)
                processing_time = time.time() - start_time

                self.compression_stats.update({
                    'bytes_processed': source_size,
                    'bytes_compressed': compressed_size,
                    'compression_ratio': source_size / compressed_size if compressed_size > 0 else 0,
                    'processing_time': processing_time
                })

                logger.info(f"Compression completed: {source_size/1024/1024:.2f}MB -> "
                           f"{compressed_size/1024/1024:.2f}MB "
                           f"(ratio: {self.compression_stats['compression_ratio']:.2f}x) "
                           f"in {processing_time:.2f}s")

                return True, output_path, self.compression_stats.copy()
            else:
                logger.error(f"Compression failed for {source_dir}")
                return False, "", {}

        except Exception as e:
            logger.error(f"Error compressing directory {source_dir}: {str(e)}")
            return False, "", {}

    def _compress_zip(self, source_dir: str, output_path: str) -> bool:
        """
        Compress directory using ZIP format.

        Args:
            source_dir: Source directory
            output_path: Output ZIP file path

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get compression level from config
            compress_level = 6
            if self.config:
                compress_level = self.config.get('storage', 'compress_level', 6)

            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED,
                               compresslevel=compress_level) as zipf:

                file_count = 0
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)

                        try:
                            zipf.write(file_path, arc_path)
                            file_count += 1

                            # Progress reporting every 100 files
                            if file_count % 100 == 0:
                                logger.debug(f"Compressed {file_count} files")

                        except Exception as e:
                            logger.warning(f"Failed to compress file {file_path}: {str(e)}")
                            continue

                self.compression_stats['files_processed'] = file_count
                logger.debug(f"ZIP compression completed: {file_count} files")
                return True

        except Exception as e:
            logger.error(f"ZIP compression failed: {str(e)}")
            return False

    def _compress_tar(self, source_dir: str, output_path: str, compression: str) -> bool:
        """
        Compress directory using TAR format with specified compression.

        Args:
            source_dir: Source directory
            output_path: Output TAR file path
            compression: Compression type ('gz', 'bz2', 'xz')

        Returns:
            True if successful, False otherwise
        """
        try:
            mode_map = {
                'gz': 'w:gz',
                'bz2': 'w:bz2',
                'xz': 'w:xz'
            }

            mode = mode_map.get(compression, 'w:gz')

            with tarfile.open(output_path, mode) as tar:
                file_count = 0

                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_dir)

                        try:
                            tar.add(file_path, arcname=arc_path)
                            file_count += 1

                            # Progress reporting every 100 files
                            if file_count % 100 == 0:
                                logger.debug(f"Compressed {file_count} files")

                        except Exception as e:
                            logger.warning(f"Failed to compress file {file_path}: {str(e)}")
                            continue

                self.compression_stats['files_processed'] = file_count
                logger.debug(f"TAR.{compression.upper()} compression completed: {file_count} files")
                return True

        except Exception as e:
            logger.error(f"TAR.{compression.upper()} compression failed: {str(e)}")
            return False

    def _calculate_directory_size(self, directory: str) -> int:
        """
        Calculate total size of directory in bytes.

        Args:
            directory: Directory path

        Returns:
            Total size in bytes
        """
        try:
            total_size = 0
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue
            return total_size
        except Exception as e:
            logger.warning(f"Error calculating directory size: {str(e)}")
            return 0

    def get_optimal_algorithm(self, source_dir: str) -> str:
        """
        Get optimal compression algorithm (always returns TAR.GZ for consistency).

        Args:
            source_dir: Source directory to analyze

        Returns:
            Recommended compression algorithm (always 'tar.gz')
        """
        try:
            # Analyze directory characteristics for logging purposes
            total_size = self._calculate_directory_size(source_dir)
            file_count = sum(len(files) for _, _, files in os.walk(source_dir))

            logger.info(f"Directory analysis: {total_size/1024/1024:.2f}MB, {file_count} files")
            logger.info("Using TAR.GZ compression (optimal balance of speed, compression, and compatibility)")

            # Always return TAR.GZ for consistent backup operations
            return 'tar.gz'

        except Exception as e:
            logger.warning(f"Error analyzing directory: {str(e)}")
            return 'tar.gz'  # Always default to GZ

    def get_compression_stats(self) -> Dict[str, Any]:
        """
        Get current compression statistics.

        Returns:
            Dictionary with compression statistics
        """
        return self.compression_stats.copy()
