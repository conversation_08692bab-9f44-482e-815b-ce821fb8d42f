"""
Metrics and monitoring data models for TNGD Backup System

This module defines data structures for system monitoring, performance tracking,
and backup metrics collection.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
import psutil


@dataclass
class SystemMetrics:
    """System resource metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    memory_available_mb: float = 0.0
    disk_usage_percent: float = 0.0
    disk_free_gb: float = 0.0
    thread_count: int = 0
    process_count: int = 0
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0
    
    @classmethod
    def collect_current(cls) -> 'SystemMetrics':
        """Collect current system metrics"""
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        return cls(
            cpu_percent=psutil.cpu_percent(interval=1),
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024 * 1024),
            memory_available_mb=memory.available / (1024 * 1024),
            disk_usage_percent=disk.percent,
            disk_free_gb=disk.free / (1024 * 1024 * 1024),
            thread_count=len(psutil.Process().threads()),
            process_count=len(psutil.pids()),
            network_sent_mb=network.bytes_sent / (1024 * 1024),
            network_recv_mb=network.bytes_recv / (1024 * 1024)
        )


@dataclass
class BackupMetrics:
    """Backup operation metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    tables_processed: int = 0
    tables_total: int = 0
    records_processed: int = 0
    records_total: int = 0
    bytes_processed: int = 0
    bytes_uploaded: int = 0
    compression_ratio: float = 0.0
    processing_rate_records_per_sec: float = 0.0
    upload_rate_mb_per_sec: float = 0.0
    errors_count: int = 0
    retries_count: int = 0
    current_table: Optional[str] = None
    current_date: Optional[str] = None
    estimated_completion: Optional[datetime] = None
    
    @property
    def progress_percent(self) -> float:
        """Calculate overall progress percentage"""
        if self.tables_total == 0:
            return 0.0
        return (self.tables_processed / self.tables_total) * 100
    
    @property
    def compression_percent(self) -> float:
        """Calculate compression percentage"""
        if self.bytes_processed == 0:
            return 0.0
        return (1 - (self.bytes_uploaded / self.bytes_processed)) * 100


@dataclass
class PerformanceMetrics:
    """Performance tracking metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    operation_name: str = ""
    duration_seconds: float = 0.0
    throughput_records_per_sec: float = 0.0
    throughput_mb_per_sec: float = 0.0
    memory_peak_mb: float = 0.0
    cpu_peak_percent: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the performance metrics"""
        self.metadata[key] = value
    
    def calculate_throughput(self, records_count: int, bytes_count: int) -> None:
        """Calculate throughput metrics"""
        if self.duration_seconds > 0:
            self.throughput_records_per_sec = records_count / self.duration_seconds
            self.throughput_mb_per_sec = (bytes_count / (1024 * 1024)) / self.duration_seconds


@dataclass
class AlertMetrics:
    """Alert and notification metrics"""
    timestamp: datetime = field(default_factory=datetime.now)
    alert_type: str = ""  # 'warning', 'error', 'critical'
    message: str = ""
    component: str = ""  # 'backup_engine', 'storage_manager', etc.
    severity: int = 1  # 1-5 scale
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    
    def resolve(self) -> None:
        """Mark alert as resolved"""
        self.resolved = True
        self.resolution_time = datetime.now()


@dataclass
class MetricsCollection:
    """Collection of all metrics for a backup session"""
    session_id: str = ""
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    system_metrics: List[SystemMetrics] = field(default_factory=list)
    backup_metrics: List[BackupMetrics] = field(default_factory=list)
    performance_metrics: List[PerformanceMetrics] = field(default_factory=list)
    alerts: List[AlertMetrics] = field(default_factory=list)
    
    def add_system_metrics(self, metrics: SystemMetrics) -> None:
        """Add system metrics to collection"""
        self.system_metrics.append(metrics)
    
    def add_backup_metrics(self, metrics: BackupMetrics) -> None:
        """Add backup metrics to collection"""
        self.backup_metrics.append(metrics)
    
    def add_performance_metrics(self, metrics: PerformanceMetrics) -> None:
        """Add performance metrics to collection"""
        self.performance_metrics.append(metrics)
    
    def add_alert(self, alert: AlertMetrics) -> None:
        """Add alert to collection"""
        self.alerts.append(alert)
    
    def get_latest_backup_metrics(self) -> Optional[BackupMetrics]:
        """Get the most recent backup metrics"""
        return self.backup_metrics[-1] if self.backup_metrics else None
    
    def get_latest_system_metrics(self) -> Optional[SystemMetrics]:
        """Get the most recent system metrics"""
        return self.system_metrics[-1] if self.system_metrics else None
    
    def get_unresolved_alerts(self) -> List[AlertMetrics]:
        """Get all unresolved alerts"""
        return [alert for alert in self.alerts if not alert.resolved]
    
    def finalize_session(self) -> None:
        """Mark the session as complete"""
        self.end_time = datetime.now()
    
    @property
    def duration_seconds(self) -> float:
        """Get session duration in seconds"""
        end = self.end_time or datetime.now()
        return (end - self.start_time).total_seconds()
