"""
TNGD Backup System

A professional backup system for TNGD data with advanced features:
- Thread management and resource optimization
- Real-time monitoring and progress tracking
- Automatic recovery and checkpoint system
- Comprehensive error handling and retry logic
- OSS storage integration with compression
"""

__version__ = "2.0.0"
__author__ = "TNGD Team"
__email__ = "<EMAIL>"

from .core.backup_engine import BackupEngine
from .core.config_manager import ConfigManager
from .utils.monitoring import ResourceMonitor
from .models.backup_config import BackupConfig
from .constants import BackupConstants, FileConstants, ConfigConstants

__all__ = [
    "BackupEngine",
    "ConfigManager",
    "ResourceMonitor",
    "BackupConfig",
    "BackupConstants",
    "FileConstants",
    "ConfigConstants"
]
