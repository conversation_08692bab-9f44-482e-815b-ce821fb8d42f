"""
Core modules for TNGD Backup System

This package contains the essential business logic and core functionality:
- BackupEngine: Main backup orchestration
- DevoClient: Devo API integration
- StorageManager: OSS storage operations
- CompressionService: File compression utilities
- StreamingProcessor: Large dataset handling
- ThreadManager: Resource management
- ConfigManager: Configuration handling
- EmailService: Notification system
"""

from .backup_engine import BackupEngine
from .devo_client import DevoClient
from .storage_manager import StorageManager
from .compression_service import CompressionService
from .streaming_processor import StreamingDataProcessor
from .thread_manager import ThreadManager
from .config_manager import ConfigManager
from .email_service import EmailService

__all__ = [
    "BackupEngine",
    "DevoClient",
    "StorageManager", 
    "CompressionService",
    "StreamingDataProcessor",
    "ThreadManager",
    "ConfigManager",
    "EmailService"
]
